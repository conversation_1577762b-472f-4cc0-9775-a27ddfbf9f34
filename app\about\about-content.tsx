"use client"

import { useEffect, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import {
  Target,
  Eye,
  BookOpen
} from "lucide-react"

export default function AboutContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <BookOpen size={16} className="mr-2" />
              About Us
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              About <span className="text-yellow-300">Dwelling Desire</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Your trusted partner in Ahmedabad's real estate journey with expertise, transparency, and commitment to excellence.
            </p>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Welcome to <span className="text-burgundy-600">Dwelling Desire</span>
              </h2>

              <div className="text-center mb-12">
                <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                  Founded in 2019, Dwelling Desire has emerged as <strong className="text-burgundy-600">Ahmedabad's premier real estate consultancy</strong>, specializing in residential and commercial properties across Gujarat's most sought-after locations.
                </p>
              </div>

              <div className="text-center mb-16">
                <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                  With deep local market knowledge and a commitment to transparent dealings, we've successfully facilitated over <strong className="text-burgundy-600">500+ property transactions</strong>, helping families find their dream homes and investors discover lucrative opportunities.
                </p>
              </div>

              {/* Mission & Vision */}
              <div className="grid md:grid-cols-2 gap-8 mb-16">
                <div className="text-center">
                  <div className="w-16 h-16 bg-burgundy-600 rounded-full flex items-center justify-center mb-6 mx-auto">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To provide exceptional real estate services that exceed client expectations through transparent dealings, expert market knowledge, and personalized attention.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-6 mx-auto">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To become Gujarat's most trusted real estate brand, known for integrity, innovation, and creating value for all stakeholders.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
