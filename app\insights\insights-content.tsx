"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  BookOpen, 
  User, 
  Calendar, 
  Clock, 
  ArrowRight, 
  Search,
  Filter,
  TrendingUp,
  Target,
  ChevronDown
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Blog data - updated with detailed content
const insights = [
  {
    id: 1,
    title: "Real Estate Market Trends in Ahmedabad 2025",
    excerpt: "Ahmedabad is experiencing a transformative shift in its real estate market. With new infrastructure projects, growing IT presence, and increasing demand for smart housing, 2025 is shaping up to be a defining year for property in Ahmedabad.",
    image: "/images/insights/1.webp",
    category: "Market Analysis",
    author: "Dwelling Desire Team",
    date: "2024-07-15",
    readTime: "8 min read",
    featured: true,
    tags: ["market trends", "ahmedabad", "investment", "2025", "infrastructure", "smart housing"],
    content: `
      <div class="blog-content">
        <p class="lead-paragraph">Ahmedabad, the cultural and commercial heartbeat of Gujarat, is experiencing a transformative shift in its real estate market. With new infrastructure projects, a growing IT presence, and increasing demand for smart housing, 2025 is shaping up to be a defining year for property in Ahmedabad.</p>

        <p>Whether you're a homebuyer, NRI investor, or real estate developer, understanding the market trends can help you make strategic decisions. In this blog, <strong>Dwelling Desire</strong> breaks down the top real estate trends in Ahmedabad for 2025 — from booming zones to pricing projections and government reforms.</p>

        <h2>📈 1. Surge in Demand for Affordable Yet Premium Housing</h2>
        <p>The post-pandemic shift in lifestyle preferences has fueled demand for affordable housing with premium features — such as gated communities, 24/7 security, green spaces, and clubhouse amenities.</p>

        <h3>Key Locations Leading the Trend:</h3>
        <ul>
          <li><strong>South Bopal and Gota:</strong> Offering 2 & 3 BHK flats under ₹65 lakhs with modern amenities</li>
          <li><strong>New SG Highway:</strong> Becoming a hotspot for mid-income families looking for lifestyle upgrades</li>
          <li><strong>Shela & Sanand:</strong> Emerging micro-markets for first-time homebuyers</li>
        </ul>

        <h2>🏗️ 2. Infrastructure Boom: Metro & Expressway Expansion</h2>
        <p>2025 will witness the impact of major infrastructural projects:</p>
        <ul>
          <li><strong>Ahmedabad Metro Phase 2:</strong> Connecting GNLU, GIFT City, and other fast-developing zones to central Ahmedabad</li>
          <li><strong>Ahmedabad-Dholera Expressway:</strong> Boosting investment potential in the Dholera SIR region and surrounding areas</li>
          <li><strong>Flyover projects</strong> near SG Highway, Iscon, and Science City to reduce congestion</li>
        </ul>
        <p>These developments are significantly raising property values near metro and highway zones, making them ideal for long-term investment.</p>

        <h2>🌱 3. Rise in Sustainable & Smart Housing Projects</h2>
        <p>Buyers in 2025 are increasingly looking for:</p>
        <ul>
          <li>Green buildings with rainwater harvesting & solar panels</li>
          <li>Smart home automation (IoT-based controls for lighting, security, appliances)</li>
          <li>Low-maintenance, eco-conscious homes</li>
        </ul>
        <p>Builders in Ahmedabad are now integrating IGBC certification and smart technology to attract modern buyers, especially the tech-savvy millennial and NRI segments.</p>

        <h2>💼 4. Commercial Real Estate on the Rise: Focus on GIFT City & SG Highway</h2>
        <p>Ahmedabad's commercial real estate is booming with:</p>
        <ul>
          <li><strong>GIFT City</strong> becoming an international financial hub with massive FDI inflow</li>
          <li><strong>Co-working spaces, tech parks, and IT zones</strong> along SG Highway, Prahlad Nagar, and Shilaj</li>
          <li>A shift toward <strong>Grade A office spaces</strong> to meet demand from fintech, IT, and e-commerce firms</li>
        </ul>
        <p>This surge is also driving rental yields up to 5-7% annually, making commercial investment highly lucrative in 2025.</p>

        <h2>🏡 5. Luxury & Villa Market Gaining Traction</h2>
        <p>While affordability remains a key driver, luxury housing is seeing strong demand from NRIs and high-net-worth individuals (HNIs). Areas like:</p>
        <ul>
          <li><strong>Thaltej, Ambli, and Shilaj</strong> now feature ultra-luxury villas and penthouses starting from ₹3 Cr onwards</li>
          <li><strong>Clubhouse-centric gated villas</strong> with smart home features are selling fast among business owners and returning NRIs</li>
        </ul>

        <h2>📊 6. Real Estate Prices in Ahmedabad 2025: What to Expect?</h2>
        <p>Here's a rough projection of average prices in key zones (based on Q1 2025 trends):</p>

        <div class="price-table">
          <table class="w-full border-collapse border border-gray-300 my-6">
            <thead>
              <tr class="bg-burgundy-50">
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Location</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Avg. Price per Sq.Ft (2025)</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Growth from 2024</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="border border-gray-300 px-4 py-3">SG Highway</td>
                <td class="border border-gray-300 px-4 py-3">₹6,200 - ₹7,800</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">+10%</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3">South Bopal</td>
                <td class="border border-gray-300 px-4 py-3">₹4,200 - ₹5,000</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">+7%</td>
              </tr>
              <tr>
                <td class="border border-gray-300 px-4 py-3">Shela</td>
                <td class="border border-gray-300 px-4 py-3">₹3,800 - ₹4,800</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">+9%</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3">GIFT City</td>
                <td class="border border-gray-300 px-4 py-3">₹7,000 - ₹10,000</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">+12%</td>
              </tr>
              <tr>
                <td class="border border-gray-300 px-4 py-3">Satellite/Thaltej</td>
                <td class="border border-gray-300 px-4 py-3">₹8,000 - ₹11,000</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">+6%</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="expert-tip bg-gradient-to-r from-burgundy-50 to-burgundy-100 p-6 rounded-xl border-l-4 border-burgundy-600 my-8">
          <h4 class="font-bold text-burgundy-800 mb-2">🏆 Expert Tip from Dwelling Desire:</h4>
          <p class="text-burgundy-700">Look for under-valued pockets near future metro lines — they hold the highest appreciation potential in 2–3 years.</p>
        </div>

        <h2>📜 7. Government Policies & RERA Boosting Transparency</h2>
        <p>Gujarat RERA continues to ensure:</p>
        <ul>
          <li>Greater transparency in builder-buyer transactions</li>
          <li>On-time project deliveries</li>
          <li>Legal clarity in documentation & dispute resolution</li>
        </ul>
        <p>Additionally, stamp duty rebates for women homebuyers, lower home loan rates (~8.5%), and PMAY subsidies are giving a strong push to first-time buyers.</p>

        <h2>🔮 8. What the Future Holds: 2026 and Beyond</h2>
        <p>By late 2025, experts predict:</p>
        <ul>
          <li>More FDI in real estate, especially in smart cities like Dholera</li>
          <li>Higher adoption of AI in real estate portals for smart search, virtual visits & predictive pricing</li>
          <li>Builders shifting to mixed-use townships offering retail, office, and residential in one zone</li>
        </ul>

        <h2>📌 Final Thoughts: Should You Buy or Invest in Ahmedabad in 2025?</h2>
        <p><strong>Absolutely.</strong> Whether you're looking for a dream home or a high-return investment, 2025 presents one of the best windows to enter the Ahmedabad property market.</p>

        <div class="cta-section bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold mb-4">🏡 Dwelling Desire is here to help you find verified, budget-friendly, and premium properties — with full transparency and expert guidance.</h3>

          <div class="grid md:grid-cols-2 gap-6 mt-6">
            <div>
              <h4 class="font-semibold mb-2">🔍 Explore Our Listings:</h4>
              <p class="text-white/90">Visit our Property Search Page to browse 100% verified listings in Ahmedabad's hottest locations.</p>
            </div>
            <div>
              <h4 class="font-semibold mb-2">📝 Talk to an Expert:</h4>
              <p class="text-white/90">Need help selecting the right property or area? Contact our team today for a free consultation.</p>
            </div>
          </div>
        </div>
      </div>
    `
  },
  {
    id: 2,
    title: "Top 10 Localities for Property Investment in Ahmedabad 2025",
    excerpt: "Explore the most promising areas in Ahmedabad for real estate investment with high growth potential. From emerging micro-markets to established premium zones, discover where smart money is flowing.",
    image: "/images/insights/2.webp",
    category: "Investment",
    author: "Rajesh Patel",
    date: "2024-06-10",
    readTime: "10 min",
    featured: false,
    tags: ["investment", "localities", "growth", "ahmedabad", "ROI", "appreciation"],
    content: `
      <div class="blog-content">
        <p class="lead-paragraph">Choosing the right locality is crucial for successful real estate investment. With Ahmedabad's rapid expansion and infrastructure development, certain areas are emerging as goldmines for property investors. Here's our comprehensive analysis of the top 10 localities that offer excellent growth potential and returns.</p>

        <h2>🥇 1. Prahlad Nagar - The Premium Investment Hub</h2>
        <div class="locality-card bg-green-50 p-6 rounded-xl border-l-4 border-green-500 my-6">
          <h4 class="font-bold text-green-800 mb-2">Investment Highlights:</h4>
          <ul class="text-green-700">
            <li><strong>Average Price:</strong> ₹7,500 - ₹9,500 per sq.ft</li>
            <li><strong>Expected Appreciation:</strong> 12-15% annually</li>
            <li><strong>Rental Yield:</strong> 3-4%</li>
            <li><strong>Best For:</strong> Luxury apartments, NRI investments</li>
          </ul>
        </div>
        <p>Known for its premium residential projects and excellent connectivity to major IT hubs. The upcoming metro connectivity will further boost property values.</p>

        <h2>🥈 2. South Bopal - The Emerging Goldmine</h2>
        <div class="locality-card bg-blue-50 p-6 rounded-xl border-l-4 border-blue-500 my-6">
          <h4 class="font-bold text-blue-800 mb-2">Investment Highlights:</h4>
          <ul class="text-blue-700">
            <li><strong>Average Price:</strong> ₹4,200 - ₹5,500 per sq.ft</li>
            <li><strong>Expected Appreciation:</strong> 15-18% annually</li>
            <li><strong>Rental Yield:</strong> 4-5%</li>
            <li><strong>Best For:</strong> First-time investors, affordable luxury</li>
          </ul>
        </div>
        <p>Rapidly developing area with excellent infrastructure and proximity to major employment centers. Perfect for investors looking for high appreciation potential.</p>

        <h2>🥉 3. GIFT City - The Financial District</h2>
        <div class="locality-card bg-purple-50 p-6 rounded-xl border-l-4 border-purple-500 my-6">
          <h4 class="font-bold text-purple-800 mb-2">Investment Highlights:</h4>
          <ul class="text-purple-700">
            <li><strong>Average Price:</strong> ₹8,000 - ₹12,000 per sq.ft</li>
            <li><strong>Expected Appreciation:</strong> 20-25% annually</li>
            <li><strong>Rental Yield:</strong> 5-7%</li>
            <li><strong>Best For:</strong> Commercial investments, high-end residential</li>
          </ul>
        </div>
        <p>India's first operational smart city and international financial services center. Massive FDI inflow making it a hotspot for commercial and luxury residential investments.</p>

        <h2>4. Shela - The Smart Choice for Budget Investors</h2>
        <p>Emerging as a preferred destination for young professionals and first-time homebuyers. With prices ranging from ₹3,800 - ₹4,800 per sq.ft, it offers excellent entry-level investment opportunities.</p>

        <h2>5. SG Highway - The Commercial Corridor</h2>
        <p>Established commercial hub with consistent rental income potential. Mixed-use developments and proximity to major IT companies make it ideal for commercial investments.</p>

        <h2>6. Satellite - The Established Premium Zone</h2>
        <p>One of Ahmedabad's most sought-after residential areas. While prices are higher (₹8,000 - ₹11,000 per sq.ft), the area offers stability and consistent appreciation.</p>

        <h2>7. Gota - The Infrastructure Beneficiary</h2>
        <p>Benefiting from metro connectivity and proximity to major employment hubs. Excellent for mid-segment housing investments with good appreciation potential.</p>

        <h2>8. Thaltej - The Luxury Segment Leader</h2>
        <p>Premium locality attracting HNIs and NRIs. High-end villas and apartments with excellent amenities. Perfect for luxury segment investments.</p>

        <h2>9. Sanand - The Industrial Growth Story</h2>
        <p>With major automotive and industrial investments, Sanand is emerging as a key investment destination for affordable housing targeting industrial workers.</p>

        <h2>10. Dholera SIR - The Future Smart City</h2>
        <p>India's first greenfield smart city project. While still developing, early investors can expect massive returns as the project takes shape.</p>

        <div class="investment-tips bg-gradient-to-r from-burgundy-50 to-burgundy-100 p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold text-burgundy-800 mb-4">💡 Investment Tips from Dwelling Desire Experts:</h3>
          <ul class="text-burgundy-700 space-y-2">
            <li>✅ <strong>Diversify:</strong> Don't put all money in one locality</li>
            <li>✅ <strong>Research:</strong> Check upcoming infrastructure projects</li>
            <li>✅ <strong>Timeline:</strong> Have a clear 3-5 year investment horizon</li>
            <li>✅ <strong>Legal Check:</strong> Ensure all approvals are in place</li>
            <li>✅ <strong>Builder Reputation:</strong> Choose established developers</li>
          </ul>
        </div>

        <h2>Investment Comparison Matrix</h2>
        <p>Based on our analysis, here's how these localities stack up for different investment goals:</p>

        <div class="comparison-grid grid md:grid-cols-3 gap-6 my-8">
          <div class="bg-green-50 p-6 rounded-xl border border-green-200">
            <h4 class="font-bold text-green-800 mb-3"> High Appreciation</h4>
            <ul class="text-green-700 text-sm space-y-1">
              <li>• GIFT City</li>
              <li>• South Bopal</li>
              <li>• Dholera SIR</li>
            </ul>
          </div>
          <div class="bg-blue-50 p-6 rounded-xl border border-blue-200">
            <h4 class="font-bold text-blue-800 mb-3">High Rental Yield</h4>
            <ul class="text-blue-700 text-sm space-y-1">
              <li>• SG Highway</li>
              <li>• Gota</li>
              <li>• South Bopal</li>
            </ul>
          </div>
          <div class="bg-purple-50 p-6 rounded-xl border border-purple-200">
            <h4 class="font-bold text-purple-800 mb-3">Stable Returns</h4>
            <ul class="text-purple-700 text-sm space-y-1">
              <li>• Prahlad Nagar</li>
              <li>• Satellite</li>
              <li>• Thaltej</li>
            </ul>
          </div>
        </div>

        <div class="cta-section bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold mb-4">Ready to Invest in Ahmedabad's Hottest Localities?</h3>
          <p class="text-white/90 mb-4">Our property experts can help you identify the perfect investment opportunity based on your budget and goals.</p>
          <div class="flex flex-col sm:flex-row gap-4">
            <button class="bg-white text-burgundy-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              View Properties
            </button>
            <button class="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-burgundy-600 transition-colors">
              Schedule Consultation
            </button>
          </div>
        </div>
      </div>
    `
  },
  {
    id: 3,
    title: "Complete Home Buying Guide for First-Time Buyers in Ahmedabad 2025",
    excerpt: "A comprehensive step-by-step guide covering everything first-time home buyers need to know about purchasing property in Ahmedabad. From financial planning to legal documentation, we've got you covered.",
    image: "/images/insights/3.webp",
    category: "Guide",
    author: "Priya Sharma",
    date: "2024-06-05",
    readTime: "12 min",
    featured: false,
    tags: ["home buying", "first time", "guide", "tips", "ahmedabad", "documentation"],
    content: `
      <div class="blog-content">
        <p class="lead-paragraph">Buying your first home is one of life's most significant milestones and investments. In Ahmedabad's dynamic real estate market, first-time buyers have excellent opportunities, but navigating the process can feel overwhelming. This comprehensive guide will walk you through every step, ensuring you make informed decisions and avoid common pitfalls.</p>

        <h2> Step 1: Assess Your Financial Readiness</h2>
        <p>Before you start house hunting, it's crucial to understand your financial position and set a realistic budget.</p>

        <h3>Calculate Your Budget</h3>
        <div class="budget-calculator bg-blue-50 p-6 rounded-xl border-l-4 border-blue-500 my-6">
          <h4 class="font-bold text-blue-800 mb-3">Budget Calculation Formula:</h4>
          <ul class="text-blue-700 space-y-2">
            <li><strong>Monthly Income:</strong> ₹X</li>
            <li><strong>Maximum EMI (40% of income):</strong> ₹X × 0.4</li>
            <li><strong>Down Payment Available:</strong> ₹Y (typically 20% of property value)</li>
            <li><strong>Maximum Property Value:</strong> Down Payment ÷ 0.2</li>
          </ul>
        </div>

        <h3>Additional Costs to Consider</h3>
        <ul>
          <li><strong>Registration & Stamp Duty:</strong> 5-7% of property value</li>
          <li><strong>Home Loan Processing:</strong> 0.5-1% of loan amount</li>
          <li><strong>Legal Verification:</strong> ₹15,000 - ₹25,000</li>
          <li><strong>Interior & Moving:</strong> ₹2-5 lakhs</li>
          <li><strong>Maintenance Deposit:</strong> ₹50,000 - ₹1 lakh</li>
        </ul>

        <h2>🏦 Step 2: Get Pre-approved for a Home Loan</h2>
        <p>Pre-approval gives you a clear budget and shows sellers you're a serious buyer.</p>

        <h3>Documents Required for Home Loan</h3>
        <div class="documents-grid grid md:grid-cols-2 gap-6 my-6">
          <div class="bg-green-50 p-6 rounded-xl border border-green-200">
            <h4 class="font-bold text-green-800 mb-3">Income Documents</h4>
            <ul class="text-green-700 text-sm space-y-1">
              <li>• Salary slips (last 3 months)</li>
              <li>• Form 16 (last 2 years)</li>
              <li>• Bank statements (last 6 months)</li>
              <li>• Employment certificate</li>
            </ul>
          </div>
          <div class="bg-purple-50 p-6 rounded-xl border border-purple-200">
            <h4 class="font-bold text-purple-800 mb-3">🆔 Identity Documents</h4>
            <ul class="text-purple-700 text-sm space-y-1">
              <li>• PAN Card</li>
              <li>• Aadhaar Card</li>
              <li>• Passport (if applicable)</li>
              <li>• Voter ID</li>
            </ul>
          </div>
        </div>

        <h3>Best Home Loan Rates in Ahmedabad (2025)</h3>
        <div class="loan-rates">
          <table class="w-full border-collapse border border-gray-300 my-6">
            <thead>
              <tr class="bg-burgundy-50">
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Bank</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Interest Rate</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Processing Fee</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="border border-gray-300 px-4 py-3">SBI</td>
                <td class="border border-gray-300 px-4 py-3">8.50% - 9.25%</td>
                <td class="border border-gray-300 px-4 py-3">0.35% + GST</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3">HDFC</td>
                <td class="border border-gray-300 px-4 py-3">8.75% - 9.50%</td>
                <td class="border border-gray-300 px-4 py-3">0.50% + GST</td>
              </tr>
              <tr>
                <td class="border border-gray-300 px-4 py-3">ICICI</td>
                <td class="border border-gray-300 px-4 py-3">8.70% - 9.40%</td>
                <td class="border border-gray-300 px-4 py-3">0.50% + GST</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2>📍 Step 3: Choose the Right Location</h2>
        <p>Location is everything in real estate. Consider these factors when selecting your area:</p>

        <h3>Key Factors to Evaluate</h3>
        <div class="location-factors grid md:grid-cols-2 gap-6 my-6">
          <div class="bg-yellow-50 p-6 rounded-xl border border-yellow-200">
            <h4 class="font-bold text-yellow-800 mb-3">🚗 Connectivity</h4>
            <ul class="text-yellow-700 text-sm space-y-1">
              <li>• Distance to workplace</li>
              <li>• Public transport availability</li>
              <li>• Metro connectivity (current/planned)</li>
              <li>• Highway access</li>
            </ul>
          </div>
          <div class="bg-orange-50 p-6 rounded-xl border border-orange-200">
            <h4 class="font-bold text-orange-800 mb-3">🏥 Amenities</h4>
            <ul class="text-orange-700 text-sm space-y-1">
              <li>• Schools and colleges</li>
              <li>• Hospitals and clinics</li>
              <li>• Shopping centers</li>
              <li>• Parks and recreation</li>
            </ul>
          </div>
        </div>

        <h3>Best Areas for First-Time Buyers in Ahmedabad</h3>
        <div class="areas-recommendation bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl my-6">
          <h4 class="font-bold text-green-800 mb-3">🏆 Recommended Areas by Budget:</h4>
          <ul class="text-green-700 space-y-2">
            <li><strong>Budget: ₹40-60 Lakhs:</strong> Gota, Shela, New Ranip</li>
            <li><strong>Budget: ₹60-80 Lakhs:</strong> South Bopal, Ognaj, Sola</li>
            <li><strong>Budget: ₹80 Lakhs+:</strong> Prahlad Nagar, Satellite, Thaltej</li>
          </ul>
        </div>

        <h2>🔍 Step 4: Property Search and Evaluation</h2>
        <p>Now comes the exciting part - finding your dream home!</p>

        <h3>Property Checklist</h3>
        <div class="property-checklist bg-blue-50 p-6 rounded-xl border-l-4 border-blue-500 my-6">
          <h4 class="font-bold text-blue-800 mb-3">✅ What to Check During Site Visits:</h4>
          <div class="grid md:grid-cols-2 gap-4">
            <ul class="text-blue-700 space-y-1">
              <li>✓ Builder's reputation and track record</li>
              <li>✓ RERA registration and approvals</li>
              <li>✓ Construction quality and materials</li>
              <li>✓ Ventilation and natural light</li>
              <li>✓ Water supply and drainage</li>
            </ul>
            <ul class="text-blue-700 space-y-1">
              <li>✓ Parking availability</li>
              <li>✓ Security arrangements</li>
              <li>✓ Amenities and facilities</li>
              <li>✓ Possession timeline</li>
              <li>✓ Resale potential</li>
            </ul>
          </div>
        </div>

        <h2>📋 Step 5: Legal Documentation and Verification</h2>
        <p>This is the most critical step. Ensure all legal aspects are thoroughly checked.</p>

        <h3>Essential Documents to Verify</h3>
        <ul>
          <li><strong>Title Deed:</strong> Confirms ownership of the land</li>
          <li><strong>Approved Building Plan:</strong> Municipal corporation approval</li>
          <li><strong>Occupancy Certificate:</strong> Permission to occupy the building</li>
          <li><strong>RERA Certificate:</strong> Project registration under RERA</li>
          <li><strong>NOC from Fire Department:</strong> Fire safety clearance</li>
          <li><strong>Environmental Clearance:</strong> If applicable</li>
        </ul>

        <div class="legal-warning bg-red-50 p-6 rounded-xl border-l-4 border-red-500 my-6">
          <h4 class="font-bold text-red-800 mb-2">⚠️ Legal Verification Warning:</h4>
          <p class="text-red-700">Never skip legal verification to save money. A small investment in legal check can save you from massive future problems. Always hire a qualified property lawyer.</p>
        </div>

        <h2>Step 6: Negotiation and Final Purchase</h2>
        <p>Once you've found the right property and completed verification, it's time to negotiate and close the deal.</p>

        <h3>Negotiation Tips</h3>
        <ul>
          <li>Research comparable property prices in the area</li>
          <li>Point out any defects or issues that need fixing</li>
          <li>Negotiate on additional costs like parking, club membership</li>
          <li>Ask for flexible payment terms</li>
          <li>Get everything in writing</li>
        </ul>

        <h3>Final Steps</h3>
        <ol>
          <li><strong>Token Amount:</strong> Pay 1-2% as booking amount</li>
          <li><strong>Agreement to Sell:</strong> Sign the preliminary agreement</li>
          <li><strong>Loan Approval:</strong> Submit property documents to bank</li>
          <li><strong>Registration:</strong> Complete at sub-registrar office</li>
          <li><strong>Possession:</strong> Take delivery and start your new journey!</li>
        </ol>

        <div class="success-tips bg-gradient-to-r from-burgundy-50 to-burgundy-100 p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold text-burgundy-800 mb-4">🎉 Congratulations on Your New Home!</h3>
          <p class="text-burgundy-700 mb-4">You've successfully navigated the home buying process. Here are some final tips for new homeowners:</p>
          <ul class="text-burgundy-700 space-y-2">
            <li>✅ Get comprehensive home insurance</li>
            <li>✅ Set up utility connections (electricity, gas, internet)</li>
            <li>✅ Register with the housing society</li>
            <li>✅ Keep all documents in a safe place</li>
            <li>✅ Plan your interior design and moving</li>
          </ul>
        </div>

        <div class="cta-section bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold mb-4">Ready to Start Your Home Buying Journey?</h3>
          <p class="text-white/90 mb-4">Dwelling Desire's expert team is here to guide you through every step of the process. From property search to legal verification, we ensure a smooth and transparent home buying experience.</p>
          <div class="flex flex-col sm:flex-row gap-4">
            <button class="bg-white text-burgundy-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              Browse Properties
            </button>
            <button class="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-burgundy-600 transition-colors">
              Get Expert Consultation
            </button>
          </div>
        </div>
      </div>
    `
  },
  {
    id: 4,
    title: "Commercial Real Estate Opportunities",
    excerpt: "Discover lucrative commercial real estate opportunities in Ahmedabad's growing business districts.",
    image: "/images/insights/4.webp",
    category: "Commercial",
    author: "Amit Desai",
    date: "2024-05-01",
    readTime: "8 min",
    featured: false,
    tags: ["commercial", "business", "opportunities", "investment"],
    content: "Analysis of commercial real estate opportunities..."
  },
  {
    id: 5,
    title: "Understanding Property Valuation Methods",
    excerpt: "Learn about different property valuation methods and how they affect your buying and selling decisions.",
    image: "/images/insights/5.webp",
    category: "Guide",
    author: "Neha Patel",
    date: "2024-04-20",
    readTime: "5 min",
    featured: false,
    tags: ["valuation", "property", "methods", "guide"],
    content: "Comprehensive guide to property valuation methods..."
  },
  {
    id: 6,
    title: "Real Estate Investment vs. Other Investment Options: Complete Analysis 2025",
    excerpt: "Compare real estate investment with stocks, bonds, and other investment vehicles to make informed decisions. Understand risk, returns, and liquidity factors.",
    image: "/images/insights/6.webp",
    category: "Investment",
    author: "Kiran Shah",
    date: "2024-04-15",
    readTime: "12 min",
    featured: false,
    tags: ["investment", "comparison", "real estate", "stocks", "portfolio", "returns"],
    content: `
      <div class="blog-content">
        <p class="lead-paragraph">When it comes to building wealth, investors have multiple options. Real estate has traditionally been considered a safe and profitable investment, but how does it stack up against other popular investment vehicles in 2025? Let's dive deep into a comprehensive comparison.</p>

        <h2>Real Estate vs. 📈 Stock Market</h2>
        <p>Both offer potential for significant returns, but with different risk profiles and liquidity characteristics.</p>

        <div class="comparison-grid grid md:grid-cols-2 gap-6 my-8">
          <div class="bg-green-50 p-6 rounded-xl border border-green-200">
            <h4 class="font-bold text-green-800 mb-3">Real Estate Advantages</h4>
            <ul class="text-green-700 text-sm space-y-2">
              <li>• Tangible asset with intrinsic value</li>
              <li>• Regular rental income</li>
              <li>• Inflation hedge</li>
              <li>• Tax benefits (depreciation, deductions)</li>
              <li>• Leverage opportunities</li>
              <li>• Lower volatility</li>
            </ul>
          </div>
          <div class="bg-blue-50 p-6 rounded-xl border border-blue-200">
            <h4 class="font-bold text-blue-800 mb-3">📈 Stock Market Advantages</h4>
            <ul class="text-blue-700 text-sm space-y-2">
              <li>• High liquidity</li>
              <li>• Lower entry barriers</li>
              <li>• Diversification opportunities</li>
              <li>• Professional management (mutual funds)</li>
              <li>• Potential for higher returns</li>
              <li>• No maintenance required</li>
            </ul>
          </div>
        </div>

        <h3>Returns Comparison (10-Year Average)</h3>
        <div class="returns-table">
          <table class="w-full border-collapse border border-gray-300 my-6">
            <thead>
              <tr class="bg-burgundy-50">
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Investment Type</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Average Annual Return</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Risk Level</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="border border-gray-300 px-4 py-3">Ahmedabad Real Estate</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">12-15%</td>
                <td class="border border-gray-300 px-4 py-3">Medium</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3">Indian Stock Market (Nifty)</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">11-14%</td>
                <td class="border border-gray-300 px-4 py-3">High</td>
              </tr>
              <tr>
                <td class="border border-gray-300 px-4 py-3">Mutual Funds (Equity)</td>
                <td class="border border-gray-300 px-4 py-3 text-green-600 font-semibold">10-13%</td>
                <td class="border border-gray-300 px-4 py-3">Medium-High</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2>🏦 Real Estate vs. 💎 Fixed Deposits</h2>
        <p>While FDs offer security, real estate provides inflation protection and higher returns.</p>

        <div class="comparison-analysis bg-yellow-50 p-6 rounded-xl border-l-4 border-yellow-500 my-6">
          <h4 class="font-bold text-yellow-800 mb-3">📊 Analysis: ₹50 Lakh Investment Over 10 Years</h4>
          <div class="grid md:grid-cols-2 gap-4">
            <div>
              <h5 class="font-semibold text-yellow-800 mb-2">Real Estate (Ahmedabad)</h5>
              <ul class="text-yellow-700 text-sm space-y-1">
                <li>• Initial Investment: ₹50 Lakhs</li>
                <li>• Annual Appreciation: 12%</li>
                <li>• Rental Income: ₹25,000/month</li>
                <li>• Total Value After 10 Years: ₹1.86 Cr</li>
                <li>• Total Rental Income: ₹36 Lakhs</li>
                <li><strong>• Total Returns: ₹2.22 Cr</strong></li>
              </ul>
            </div>
            <div>
              <h5 class="font-semibold text-yellow-800 mb-2">Fixed Deposit (7% p.a.)</h5>
              <ul class="text-yellow-700 text-sm space-y-1">
                <li>• Initial Investment: ₹50 Lakhs</li>
                <li>• Annual Interest: 7%</li>
                <li>• Compounding: Annually</li>
                <li>• Value After 10 Years: ₹98.4 Lakhs</li>
                <li>• Total Interest: ₹48.4 Lakhs</li>
                <li><strong>• Total Returns: ₹98.4 Lakhs</strong></li>
              </ul>
            </div>
          </div>
        </div>

        <h2>🥇 Real Estate vs. 🏆 Gold</h2>
        <p>Both are considered safe havens, but real estate offers income generation potential.</p>

        <div class="gold-comparison grid md:grid-cols-2 gap-6 my-8">
          <div class="bg-orange-50 p-6 rounded-xl border border-orange-200">
            <h4 class="font-bold text-orange-800 mb-3">Real Estate Benefits</h4>
            <ul class="text-orange-700 text-sm space-y-2">
              <li>• Generates rental income</li>
              <li>• Can be leveraged</li>
              <li>• Utility value (can live in it)</li>
              <li>• Better long-term appreciation</li>
              <li>• Tax advantages</li>
            </ul>
          </div>
          <div class="bg-yellow-50 p-6 rounded-xl border border-yellow-200">
            <h4 class="font-bold text-yellow-800 mb-3">🥇 Gold Benefits</h4>
            <ul class="text-yellow-700 text-sm space-y-2">
              <li>• High liquidity</li>
              <li>• No maintenance costs</li>
              <li>• Global acceptance</li>
              <li>• Crisis hedge</li>
              <li>• Easy storage (digital gold)</li>
            </ul>
          </div>
        </div>

        <h2>🎯 Portfolio Allocation Strategy</h2>
        <p>Smart investors don't choose one over the other - they create a balanced portfolio.</p>

        <h3>Recommended Portfolio Mix by Age Group</h3>
        <div class="portfolio-grid grid md:grid-cols-3 gap-6 my-8">
          <div class="bg-green-50 p-6 rounded-xl border border-green-200">
            <h4 class="font-bold text-green-800 mb-3">👨‍💼 Age 25-35</h4>
            <ul class="text-green-700 text-sm space-y-2">
              <li>• Real Estate: 30%</li>
              <li>• Equity/Stocks: 50%</li>
              <li>• Fixed Income: 15%</li>
              <li>• Gold: 5%</li>
            </ul>
            <p class="text-green-600 text-xs mt-3 font-semibold">Focus: Growth & Wealth Building</p>
          </div>
          <div class="bg-blue-50 p-6 rounded-xl border border-blue-200">
            <h4 class="font-bold text-blue-800 mb-3">👨‍💼 Age 35-50</h4>
            <ul class="text-blue-700 text-sm space-y-2">
              <li>• Real Estate: 40%</li>
              <li>• Equity/Stocks: 35%</li>
              <li>• Fixed Income: 20%</li>
              <li>• Gold: 5%</li>
            </ul>
            <p class="text-blue-600 text-xs mt-3 font-semibold">Focus: Balanced Growth & Stability</p>
          </div>
          <div class="bg-purple-50 p-6 rounded-xl border border-purple-200">
            <h4 class="font-bold text-purple-800 mb-3">👨‍💼 Age 50+</h4>
            <ul class="text-purple-700 text-sm space-y-2">
              <li>• Real Estate: 35%</li>
              <li>• Equity/Stocks: 25%</li>
              <li>• Fixed Income: 30%</li>
              <li>• Gold: 10%</li>
            </ul>
            <p class="text-purple-600 text-xs mt-3 font-semibold">Focus: Income & Capital Preservation</p>
          </div>
        </div>

        <h2>🔍 Key Factors to Consider</h2>

        <h3>When to Choose Real Estate</h3>
        <ul>
          <li>You have a long-term investment horizon (5+ years)</li>
          <li>You want regular income through rentals</li>
          <li>You prefer tangible assets</li>
          <li>You can handle illiquidity</li>
          <li>You want to use leverage</li>
        </ul>

        <h3>When to Choose Stocks/Mutual Funds</h3>
        <ul>
          <li>You need liquidity</li>
          <li>You want to start with smaller amounts</li>
          <li>You prefer professional management</li>
          <li>You want easy diversification</li>
          <li>You can handle volatility</li>
        </ul>

        <div class="expert-recommendation bg-gradient-to-r from-burgundy-50 to-burgundy-100 p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold text-burgundy-800 mb-4">🎯 Dwelling Desire's Expert Recommendation</h3>
          <p class="text-burgundy-700 mb-4">For most investors in Ahmedabad, real estate should form the cornerstone of their investment portfolio, especially given the city's growth trajectory. However, diversification is key to managing risk and maximizing returns.</p>

          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-semibold text-burgundy-800 mb-2">✅ Start With Real Estate If:</h4>
              <ul class="text-burgundy-700 text-sm space-y-1">
                <li>• You have ₹25+ lakhs to invest</li>
                <li>• You're looking for stable returns</li>
                <li>• You want rental income</li>
                <li>• You're planning for retirement</li>
              </ul>
            </div>
            <div>
              <h4 class="font-semibold text-burgundy-800 mb-2">📈 Add Stocks/MFs For:</h4>
              <ul class="text-burgundy-700 text-sm space-y-1">
                <li>• Higher growth potential</li>
                <li>• Liquidity needs</li>
                <li>• Smaller investment amounts</li>
                <li>• Portfolio diversification</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>📊 Tax Implications Comparison</h2>
        <div class="tax-comparison">
          <table class="w-full border-collapse border border-gray-300 my-6">
            <thead>
              <tr class="bg-burgundy-50">
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Aspect</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Real Estate</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Stocks/MF</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="border border-gray-300 px-4 py-3 font-semibold">Short-term Capital Gains</td>
                <td class="border border-gray-300 px-4 py-3">As per income tax slab</td>
                <td class="border border-gray-300 px-4 py-3">15% (Equity), Slab rate (Debt)</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3 font-semibold">Long-term Capital Gains</td>
                <td class="border border-gray-300 px-4 py-3">20% with indexation</td>
                <td class="border border-gray-300 px-4 py-3">10% above ₹1 lakh (Equity)</td>
              </tr>
              <tr>
                <td class="border border-gray-300 px-4 py-3 font-semibold">Income Tax</td>
                <td class="border border-gray-300 px-4 py-3">Rental income taxable</td>
                <td class="border border-gray-300 px-4 py-3">Dividend taxable</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3 font-semibold">Deductions</td>
                <td class="border border-gray-300 px-4 py-3">₹2L (80C), ₹1.5L (24b)</td>
                <td class="border border-gray-300 px-4 py-3">₹1.5L (80C) for ELSS</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2>🎯 Final Verdict: The Winner?</h2>
        <p>There's no single winner - it depends on your financial goals, risk tolerance, and investment timeline. However, for wealth building in Ahmedabad, real estate offers compelling advantages:</p>

        <div class="final-verdict bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border-l-4 border-green-500 my-6">
          <h4 class="font-bold text-green-800 mb-3">🏆 Why Real Estate Wins for Long-term Wealth Building:</h4>
          <ul class="text-green-700 space-y-2">
            <li>✅ <strong>Dual Income:</strong> Capital appreciation + rental income</li>
            <li>✅ <strong>Inflation Hedge:</strong> Property values and rents increase with inflation</li>
            <li>✅ <strong>Leverage:</strong> Use bank loans to multiply returns</li>
            <li>✅ <strong>Tax Benefits:</strong> Multiple deductions and exemptions</li>
            <li>✅ <strong>Tangible Asset:</strong> Physical ownership provides security</li>
            <li>✅ <strong>Forced Savings:</strong> EMI payments build equity over time</li>
          </ul>
        </div>

        <div class="cta-section bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white p-8 rounded-xl my-8">
          <h3 class="text-2xl font-bold mb-4">Ready to Start Your Real Estate Investment Journey?</h3>
          <p class="text-white/90 mb-4">Dwelling Desire's investment experts can help you identify high-potential properties that align with your financial goals and risk profile.</p>
          <div class="flex flex-col sm:flex-row gap-4">
            <button class="bg-white text-burgundy-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              Explore Investment Properties
            </button>
            <button class="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-burgundy-600 transition-colors">
              Get Investment Consultation
            </button>
          </div>
        </div>
      </div>
    `
  }
]

const categories = ["All", "Market Analysis", "Investment", "Guide", "Commercial"]

export default function InsightsPageContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [filteredInsights, setFilteredInsights] = useState(insights)
  const [showFilters, setShowFilters] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)

  // Filter insights based on search and category
  useEffect(() => {
    let filtered = insights

    if (selectedCategory !== "All") {
      filtered = filtered.filter(insight => insight.category === selectedCategory)
    }

    if (searchTerm) {
      filtered = filtered.filter(insight => 
        insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    setFilteredInsights(filtered)
  }, [searchTerm, selectedCategory])

  // Animation observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [filteredInsights])

  const featuredInsight = insights.find(insight => insight.featured)

  return (
    <main ref={sectionRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <BookOpen size={16} className="mr-2" />
              Expert Insights
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Real Estate <span className="text-yellow-300">Insights</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Stay informed with expert insights, market trends, and comprehensive guides 
              to make smart real estate decisions.
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-12 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              {/* Search Bar */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <Input
                  type="text"
                  placeholder="Search insights, guides, and market trends..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                />
              </div>
              
              {/* Filter Toggle */}
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="h-12 px-6 rounded-xl border border-gray-200 hover:bg-gray-50"
              >
                <Filter size={16} className="mr-2" />
                Filters
                <ChevronDown size={16} className={`ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
              </Button>
            </div>

            {/* Category Filters */}
            {showFilters && (
              <div className="animate-on-scroll opacity-0 translate-y-4 bg-gray-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-900 mb-4">Categories</h4>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className={`rounded-full ${
                        selectedCategory === category 
                          ? 'bg-burgundy-600 hover:bg-burgundy-700 text-white' 
                          : 'border-gray-200 text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Featured Article Section */}
      {featuredInsight && (
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <TrendingUp size={16} className="mr-2" />
                  Featured Article
                </Badge>
                <h2 className="text-3xl font-bold text-gray-900">
                  Editor's <span className="text-burgundy-600">Pick</span>
                </h2>
              </div>

              <Card className="animate-on-scroll opacity-0 translate-y-8 overflow-hidden border-0 shadow-xl bg-white">
                <div className="grid lg:grid-cols-5 gap-0">
                  <div className="lg:col-span-3 p-8 lg:p-12">
                    <Badge className="bg-burgundy-100 text-burgundy-700 hover:bg-burgundy-100 mb-6 px-4 py-2">
                      {featuredInsight.category}
                    </Badge>
                    <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                      {featuredInsight.title}
                    </h3>
                    <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                      {featuredInsight.excerpt}
                    </p>

                    <div className="flex items-center space-x-6 mb-8 text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <User size={16} />
                        <span>{featuredInsight.author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar size={16} />
                        <span suppressHydrationWarning>
                          {new Date(featuredInsight.date).toLocaleDateString('en-IN')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock size={16} />
                        <span>{featuredInsight.readTime}</span>
                      </div>
                    </div>

                    <Button
                      className="bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 py-3 rounded-full font-semibold group"
                      asChild
                    >
                      <Link href={`/insights/${featuredInsight.id}`}>
                        Read Full Article
                        <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={16} />
                      </Link>
                    </Button>
                  </div>

                  <div className="lg:col-span-2 relative">
                    <Image
                      src={featuredInsight.image || "/placeholder.svg"}
                      alt={featuredInsight.title}
                      width={600}
                      height={400}
                      className="w-full h-64 lg:h-full object-cover"
                    />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </section>
      )}

      {/* All Articles Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-12">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  All <span className="text-burgundy-600">Insights</span>
                </h2>
                <p className="text-gray-600">
                  {filteredInsights.length} article{filteredInsights.length !== 1 ? 's' : ''} found
                  {selectedCategory !== "All" && ` in ${selectedCategory}`}
                  {searchTerm && ` for "${searchTerm}"`}
                </p>
              </div>
            </div>

            {filteredInsights.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredInsights.map((article, index) => (
                  <Card
                    key={article.id}
                    className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-lg transition-all duration-300 border-0 bg-white overflow-hidden hover:-translate-y-1`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="relative overflow-hidden">
                      <Image
                        src={article.image || "/placeholder.svg"}
                        alt={article.title}
                        width={400}
                        height={250}
                        className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-white/90 text-gray-700 hover:bg-white/90 text-xs font-medium">
                          {article.category}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-burgundy-600 transition-colors">
                        {article.title}
                      </h3>

                      <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3">
                        {article.excerpt}
                      </p>

                      <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                        <div className="flex items-center space-x-2">
                          <User size={14} />
                          <span>{article.author}</span>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span suppressHydrationWarning>
                            {new Date(article.date).toLocaleDateString('en-IN')}
                          </span>
                          <span>•</span>
                          <span>{article.readTime}</span>
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 p-0 h-auto font-semibold group/btn w-full justify-start"
                        asChild
                      >
                        <Link href={`/insights/${article.id}`}>
                          Read More
                          <ArrowRight className="ml-2 group-hover/btn:translate-x-1 transition-transform" size={14} />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setSelectedCategory("All")
                  }}
                  className="border-burgundy-200 text-burgundy-600 hover:bg-burgundy-50"
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Card className="animate-on-scroll opacity-0 translate-y-8 border-0 bg-white shadow-lg overflow-hidden">
              <CardContent className="p-8 lg:p-12 text-center">
                <div className="mb-6">
                  <Target className="w-12 h-12 text-burgundy-600 mx-auto mb-4" />
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    Stay Updated with Market Insights
                  </h3>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Get the latest real estate insights, market trends, and investment opportunities
                    delivered directly to your inbox.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                  />
                  <Button className="h-12 bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 rounded-xl font-semibold">
                    Subscribe
                  </Button>
                </div>

                <p className="text-sm text-gray-500 mt-4">
                  No spam, unsubscribe at any time.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  )
}
