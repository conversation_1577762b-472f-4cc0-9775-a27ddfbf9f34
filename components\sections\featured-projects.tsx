"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Bed, Bath, Square, ArrowRight, Star, Calendar } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const projects = [
  {
    id: 1,
    title: "Skyline Residences",
    location: "Prahlad Nagar, Ahmedabad",
    price: "₹1.2 Cr - ₹2.8 Cr",
    type: "New Launch",
    status: "Under Construction",
    image: "/images/projects/1.webp",
    beds: "2-4 BHK",
    baths: "2-3",
    area: "1200-2500 sq ft",
    rating: 4.8,
    possession: "Dec 2025",
    features: ["Swimming Pool", "Gym", "Garden", "Security"],
  },
  {
    id: 2,
    title: "Golden Heights",
    location: "Satellite, Ahmedabad",
    price: "₹85 L - ₹1.5 Cr",
    type: "Ready to Move",
    status: "Available",
    image: "/images/projects/2.webp",
    beds: "2-3 BHK",
    baths: "2",
    area: "900-1800 sq ft",
    rating: 4.6,
    possession: "Immediate",
    features: ["Parking", "Lift", "Power Backup", "Water Supply"],
  },
  {
    id: 3,
    title: "Elite Commercial Hub",
    location: "SG Highway, Ahmedabad",
    price: "₹50 L - ₹2 Cr",
    type: "Commercial",
    status: "Pre-Launch",
    image: "/images/projects/3.webp",
    beds: "Office Spaces",
    baths: "Common",
    area: "500-3000 sq ft",
    rating: 4.9,
    possession: "Mar 2026",
    features: ["Central AC", "High Speed Elevators", "Food Court", "Parking"],
  },
]

export default function FeaturedProjects() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-on-scroll opacity-0 translate-y-8">
          <div className="inline-flex items-center space-x-2 bg-burgundy-100 text-burgundy-700 rounded-full px-4 py-2 text-sm font-medium mb-4">
            <Star size={16} />
            <span>Featured Projects</span>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Premium{" "}
            <span className="relative">
              <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
                Properties
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full"></div>
            </span>{" "}
            Collection
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover our handpicked selection of luxury properties that define modern living and investment excellence.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-12">
          {projects.map((project, index) => (
            <Card
              key={project.id}
              className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-2xl transition-all duration-500 border-0 bg-white overflow-hidden hover:-translate-y-2`}
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="relative overflow-hidden">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  width={400}
                  height={300}
                  className="w-full h-48 lg:h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute top-3 left-3 flex gap-2">
                  <Badge
                    className={`text-xs ${
                      project.type === "New Launch"
                        ? "bg-green-500"
                        : project.type === "Ready to Move"
                          ? "bg-blue-500"
                          : "bg-purple-500"
                    } text-white`}
                  >
                    {project.type}
                  </Badge>
                  <Badge variant="secondary" className="bg-white/90 text-gray-700 text-xs">
                    {project.status}
                  </Badge>
                </div>
                <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                  <Star className="text-yellow-500 fill-current" size={12} />
                  <span className="text-xs font-medium">{project.rating}</span>
                </div>
              </div>

              <CardContent className="p-5 lg:p-6">
                <div className="mb-4">
                  <h3 className="text-lg lg:text-2xl font-bold text-gray-900 mb-2 group-hover:text-burgundy-600 transition-colors">
                    {project.title}
                  </h3>
                  <div className="flex items-center text-gray-500 mb-3">
                    <MapPin size={14} className="mr-2" />
                    <span className="text-sm">{project.location}</span>
                  </div>
                  <div className="text-xl lg:text-2xl font-bold text-burgundy-600 mb-4">{project.price}</div>
                </div>

                {/* Property Details */}
                <div className="grid grid-cols-3 gap-3 lg:gap-4 mb-4 lg:mb-6 p-3 lg:p-4 bg-gray-50 rounded-xl">
                  <div className="text-center">
                    <Bed className="text-gray-400 mx-auto mb-1" size={16} />
                    <div className="text-xs lg:text-sm font-medium text-gray-700">{project.beds}</div>
                  </div>
                  <div className="text-center">
                    <Bath className="text-gray-400 mx-auto mb-1" size={16} />
                    <div className="text-xs lg:text-sm font-medium text-gray-700">{project.baths}</div>
                  </div>
                  <div className="text-center">
                    <Square className="text-gray-400 mx-auto mb-1" size={16} />
                    <div className="text-xs lg:text-sm font-medium text-gray-700">{project.area}</div>
                  </div>
                </div>

                {/* Possession */}
                <div className="flex items-center justify-between mb-4 lg:mb-6 p-3 bg-burgundy-50 rounded-lg">
                  <div className="flex items-center">
                    <Calendar className="text-burgundy-600 mr-2" size={14} />
                    <span className="text-sm font-medium text-burgundy-700">Possession</span>
                  </div>
                  <span className="text-sm font-bold text-burgundy-600">{project.possession}</span>
                </div>

                {/* Features */}
                <div className="mb-4 lg:mb-6">
                  <div className="flex flex-wrap gap-1.5">
                    {project.features.slice(0, 3).map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {project.features.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{project.features.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-2 lg:gap-3">
                  <Button
                    className="flex-1 bg-burgundy-600 hover:bg-burgundy-700 text-white rounded-full font-medium text-sm"
                    asChild
                  >
                    <Link href={`/projects/${project.id}`}>View Details</Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1 sm:flex-none border-burgundy-200 text-burgundy-600 hover:bg-burgundy-50 rounded-full font-medium bg-transparent text-sm"
                    asChild
                  >
                    <Link href="/contact">Enquire</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Projects CTA */}
        <div className="text-center animate-on-scroll opacity-0 translate-y-8 delay-600">
          <Button
            size="lg"
            variant="outline"
            className="border-2 border-burgundy-600 text-burgundy-600 hover:bg-burgundy-600 hover:text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 group bg-transparent"
            asChild
          >
            <Link href="/projects">
              View All Projects
              <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
